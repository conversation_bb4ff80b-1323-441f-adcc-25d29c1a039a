/* ملف الأنماط للتطبيق - تصميم عصري مسطح */

/* النافذة الرئيسية */
QMainWindow {
    background-color: #f8f9fa;
    color: #2c3e50;
    font-family: "Segoe UI", "<PERSON><PERSON><PERSON>", "Arial";
    font-size: 12px;
}

/* الشريط الجانبي */
#sidebar {
    background-color: #34495e;
    border: none;
    min-width: 250px;
    max-width: 250px;
}

/* أزرار الشريط الجانبي */
#sidebar QPushButton {
    background-color: transparent;
    color: #ecf0f1;
    border: none;
    padding: 15px 20px;
    text-align: right;
    font-size: 14px;
    font-weight: bold;
    border-radius: 0px;
}

#sidebar QPushButton:hover {
    background-color: #3498db;
    color: white;
}

#sidebar QPushButton:pressed {
    background-color: #2980b9;
}

#sidebar QPushButton:checked {
    background-color: #3498db;
    color: white;
    border-left: 4px solid #e74c3c;
}

/* المنطقة الرئيسية */
#main_content {
    background-color: #ffffff;
    border: none;
    border-radius: 10px;
    margin: 10px;
}

/* العناوين */
QLabel#title {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    padding: 20px;
    background-color: transparent;
}

QLabel#subtitle {
    font-size: 16px;
    color: #7f8c8d;
    padding: 10px 20px;
}

/* البطاقات */
QFrame#card {
    background-color: #ffffff;
    border: 1px solid #ecf0f1;
    border-radius: 10px;
    padding: 20px;
    margin: 10px;
}

QFrame#stat_card {
    background-color: #3498db;
    border: none;
    border-radius: 15px;
    padding: 15px;
    margin: 5px;
    min-width: 180px;
    min-height: 100px;
}

QFrame#stat_card QLabel {
    color: white;
    font-weight: bold;
}

/* حقول الإدخال */
QLineEdit {
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    background-color: #ffffff;
    color: #2c3e50;
}

QLineEdit:focus {
    border-color: #3498db;
    outline: none;
}

QLineEdit:hover {
    border-color: #95a5a6;
}

/* القوائم المنسدلة */
QComboBox {
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    background-color: #ffffff;
    color: #2c3e50;
    min-height: 20px;
}

QComboBox:focus {
    border-color: #3498db;
}

QComboBox::drop-down {
    border: none;
    width: 30px;
}

QComboBox::down-arrow {
    image: url(icons/arrow_down.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    background-color: #ffffff;
    selection-background-color: #3498db;
    selection-color: white;
    padding: 5px;
}

/* مربعات النص الكبيرة */
QTextEdit {
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    background-color: #ffffff;
    color: #2c3e50;
}

QTextEdit:focus {
    border-color: #3498db;
}

/* الأزرار */
QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 25px;
    font-size: 14px;
    font-weight: bold;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed {
    background-color: #21618c;
}

QPushButton:disabled {
    background-color: #bdc3c7;
    color: #7f8c8d;
}

/* أزرار خاصة */
QPushButton#success_btn {
    background-color: #27ae60;
}

QPushButton#success_btn:hover {
    background-color: #229954;
}

QPushButton#danger_btn {
    background-color: #e74c3c;
}

QPushButton#danger_btn:hover {
    background-color: #c0392b;
}

QPushButton#warning_btn {
    background-color: #f39c12;
}

QPushButton#warning_btn:hover {
    background-color: #e67e22;
}

/* الجداول */
QTableWidget {
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    background-color: #ffffff;
    alternate-background-color: #f8f9fa;
    gridline-color: #ecf0f1;
    selection-background-color: #3498db;
    selection-color: white;
}

QTableWidget::item {
    padding: 12px;
    border: none;
}

QTableWidget::item:selected {
    background-color: #3498db;
    color: white;
}

QHeaderView::section {
    background-color: #34495e;
    color: white;
    padding: 12px;
    border: none;
    font-weight: bold;
}

/* شريط التمرير */
QScrollBar:vertical {
    border: none;
    background-color: #ecf0f1;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #bdc3c7;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

/* التقويم */
QCalendarWidget {
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    background-color: #ffffff;
}

QCalendarWidget QToolButton {
    background-color: #34495e;
    color: white;
    border: none;
    padding: 8px;
    font-weight: bold;
}

QCalendarWidget QToolButton:hover {
    background-color: #3498db;
}

QCalendarWidget QAbstractItemView {
    selection-background-color: #3498db;
    selection-color: white;
}

/* رسائل التنبيه */
QMessageBox {
    background-color: #ffffff;
    color: #2c3e50;
}

QMessageBox QPushButton {
    min-width: 80px;
    padding: 8px 16px;
}

/* شريط التقدم */
QProgressBar {
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    background-color: #ecf0f1;
    text-align: center;
    font-weight: bold;
    color: #2c3e50;
}

QProgressBar::chunk {
    background-color: #3498db;
    border-radius: 6px;
}

/* التبويبات */
QTabWidget::pane {
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    background-color: #ffffff;
}

QTabBar::tab {
    background-color: #ecf0f1;
    color: #2c3e50;
    padding: 12px 20px;
    margin-right: 2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

QTabBar::tab:selected {
    background-color: #3498db;
    color: white;
}

QTabBar::tab:hover {
    background-color: #bdc3c7;
}

/* أنماط صفحة الميزانيات */
#budget_card {
    background-color: #ffffff;
    border: 2px solid #ecf0f1;
    border-radius: 12px;
    padding: 15px;
    margin: 5px;
}

#budget_card:hover {
    border-color: #3498db;
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.2);
}

#card_title {
    font-size: 16px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
}

#percentage_label {
    font-size: 14px;
    font-weight: bold;
    padding: 5px;
    border-radius: 5px;
    background-color: #ecf0f1;
}

#date_selector {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 15px;
}

/* أنماط صفحة الأهداف */
#goal_card {
    background-color: #ffffff;
    border: 2px solid #ecf0f1;
    border-radius: 12px;
    padding: 20px;
    margin: 8px;
}

#goal_card:hover {
    border-color: #27ae60;
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.2);
}

#goal_name {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 8px;
}

#stat_card {
    background-color: #ffffff;
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    padding: 15px;
    margin: 5px;
}

#stat_title {
    font-size: 12px;
    color: #7f8c8d;
    font-weight: normal;
}

#stat_value {
    font-size: 18px;
    font-weight: bold;
    margin-top: 5px;
}

#chart_title {
    font-size: 14px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
}

#toolbar {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 15px;
}

/* أزرار إضافية */
QPushButton#primary_button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: bold;
    font-size: 13px;
}

QPushButton#primary_button:hover {
    background-color: #2980b9;
}

QPushButton#primary_button:pressed {
    background-color: #21618c;
}

QPushButton#danger_button {
    background-color: #e74c3c;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: bold;
    font-size: 12px;
}

QPushButton#danger_button:hover {
    background-color: #c0392b;
}

QPushButton#danger_button:pressed {
    background-color: #a93226;
}

/* شريط التقدم */
QProgressBar {
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    text-align: center;
    font-weight: bold;
    font-size: 12px;
    background-color: #f8f9fa;
    height: 25px;
}

QProgressBar::chunk {
    background-color: #3498db;
    border-radius: 6px;
}

/* تحسينات إضافية للتجاوب */
QScrollArea {
    border: none;
    background-color: transparent;
}

QScrollBar:vertical {
    background-color: #ecf0f1;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #bdc3c7;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

/* تحسينات للجداول */
QTableWidget {
    gridline-color: #ecf0f1;
    background-color: #ffffff;
    alternate-background-color: #f8f9fa;
    selection-background-color: #3498db;
    selection-color: white;
}

QHeaderView::section {
    background-color: #34495e;
    color: white;
    padding: 10px;
    border: none;
    font-weight: bold;
}

/* أنماط صفحة التنبيهات */
#notification_card {
    background-color: #ffffff;
    border: 1px solid #ecf0f1;
    border-radius: 10px;
    padding: 15px;
    margin: 5px;
}

#notification_card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#notification_icon {
    font-size: 20px;
    margin-right: 10px;
}

#notification_title {
    font-size: 16px;
    font-weight: bold;
    color: #2c3e50;
}

#notification_time {
    font-size: 12px;
    color: #7f8c8d;
    font-style: italic;
}

#notification_message {
    font-size: 14px;
    color: #34495e;
    margin: 10px 0;
    line-height: 1.4;
}

#unread_counter {
    font-size: 14px;
    font-weight: bold;
    color: #e74c3c;
    background-color: #fdf2f2;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #fadbd8;
}

/* تحسينات إضافية للتفاعل */
QPushButton:disabled {
    background-color: #bdc3c7;
    color: #7f8c8d;
    border: 1px solid #95a5a6;
}

QComboBox {
    border: 2px solid #ecf0f1;
    border-radius: 6px;
    padding: 8px 12px;
    background-color: #ffffff;
    font-size: 13px;
}

QComboBox:focus {
    border-color: #3498db;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #7f8c8d;
    margin-right: 5px;
}

QSpinBox, QDoubleSpinBox {
    border: 2px solid #ecf0f1;
    border-radius: 6px;
    padding: 8px 12px;
    background-color: #ffffff;
    font-size: 13px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #3498db;
}

QCheckBox {
    font-size: 13px;
    color: #2c3e50;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border: 2px solid #bdc3c7;
    border-radius: 4px;
    background-color: #ffffff;
}

QCheckBox::indicator:checked {
    background-color: #3498db;
    border-color: #3498db;
    image: none;
}

QCheckBox::indicator:checked:after {
    content: "✓";
    color: white;
    font-weight: bold;
}
